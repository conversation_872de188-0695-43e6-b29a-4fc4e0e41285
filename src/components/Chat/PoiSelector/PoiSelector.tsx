// 商家选择器
import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import { apiCaller } from '@mfe/cc-api-caller-bee';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    Dimensions,
    Platform,
    TouchableWithoutFeedback,
    FlatList,
    RefreshControl,
} from '@mrn/react-native';
import { useDebounceEffect, usePagination } from 'ahooks';
import React, { useEffect, useRef, useState } from 'react';

import noContentImg from '../../../assets/images/noContent.png';
import closeBlackImg from '../../../assets/images/poiSelector/closeBlack.png';
import useKeyboard from '../../../hooks/useKeyboard';
import { useSendMessage } from '../../../hooks/useSendMessage';
import { useUiState } from '../../../store/uiState';
import TWS from '../../../TWS';
import { EntryPointType } from '../../../types';
import {
    CardWithAvatarMessage,
    Config,
    SelectorItemMessage,
} from '../../../types/message';
import Condition from '../../Condition/Condition';
import SearchBar from '../../SearchBar';

const getPoiListByPage = async (pageNum, pageSize = 10, keyword = '') => {
    const res = await apiCaller.get(
        '/bee/v1/bdaiassistant/common/getOwnPoiListByPage',
        { pageSize, pageNum, data: keyword },
    );
    if (res.code === 0) {
        return {
            list: res.data?.poiList || [],
            total: res.data?.total,
        };
    }
    return null;
};
interface PoiItem {
    onPress: () => void;
    isLast?: boolean;
    id?: string | number;
    name: string;
    url?: string;
    online?: boolean;
    labels?: SelectorItemMessage['insert']['selectorItem']['content'];
    tags?: string[];
}
export const PoiItem = ({
    onPress,
    isLast,
    id,
    name,
    url,
    online,
    labels,
    tags,
}: PoiItem) => {
    const TheView: any = onPress ? TouchableOpacity : View;
    return (
        <TheView
            onPress={onPress}
            style={[
                TWS.row(),
                {
                    borderWidth: 0.5,
                    borderRadius: 10,
                    borderColor: '#E7E8E9',
                    padding: 12,
                    marginBottom: !isLast ? 10 : 0,
                },
            ]}
        >
            <View
                style={{
                    borderRadius: 8,
                    marginRight: 8,
                    overflow: 'hidden',
                }}
            >
                <Image
                    source={{ uri: url }}
                    style={[TWS.square(36), { borderRadius: 8 }]}
                />
            </View>

            <View style={{ flex: 1 }}>
                <View style={[TWS.row()]}>
                    <Text
                        numberOfLines={1}
                        ellipsizeMode={'middle'}
                        style={{
                            color: '#222',
                            fontSize: 16,
                            flex: 1,
                        }}
                    >
                        {name}
                    </Text>
                    <Condition condition={[online !== undefined]}>
                        <View
                            style={[
                                TWS.row(),
                                TWS.center(),
                                {
                                    borderRadius: 8,
                                    backgroundColor: online
                                        ? '#E6FAF2'
                                        : '#F1F1F1',
                                    paddingHorizontal: 4,
                                    paddingVertical: 2,
                                },
                            ]}
                        >
                            <View
                                style={[
                                    TWS.circle(8),
                                    {
                                        backgroundColor: online
                                            ? '#58C080'
                                            : '#858688',
                                        marginRight: 4,
                                    },
                                ]}
                            />
                            <Text style={{ fontSize: 10 }}>
                                {online ? '在线' : '下线'}
                            </Text>
                        </View>
                    </Condition>
                </View>

                <Condition condition={[Boolean(id)]}>
                    <Text
                        style={{
                            color: '#666',
                            fontSize: 14,
                        }}
                    >
                        ID:{id}
                    </Text>
                </Condition>
                <View
                    style={{
                        flexWrap: 'wrap',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginTop: 2,
                    }}
                >
                    {labels?.map((v) => (
                        <Text style={{ color: '#666' }} key={String(v.value)}>
                            {v.label}:{v.value}
                        </Text>
                    ))}
                    {tags?.map((v) => (
                        <View
                            style={{
                                borderColor: '#666',
                                borderWidth: 0.5,
                                borderRadius: 4,
                                paddingHorizontal: 4,
                                paddingVertical: 2,
                            }}
                            key={v}
                        >
                            <Text style={{ fontSize: 10 }}>{v}</Text>
                        </View>
                    ))}
                </View>
            </View>
        </TheView>
    );
};

const PoiSelector = () => {
    const [searchValue, setSearchValue] = useState('');
    const { data, pagination } = usePagination(
        async ({ current, pageSize = 10 }) => {
            const resData = await getPoiListByPage(
                current,
                pageSize,
                searchValue,
            );
            return resData
                ? {
                      list: [
                          ...(current === 1 ? [] : data?.list || []),
                          ...resData.list,
                      ],
                      total: resData.total,
                  }
                : data;
        },
    );
    const { list: poiList } = data || {};
    useDebounceEffect(() => {
        pagination.onChange(1, 10);
    }, [searchValue]);

    const { poiSelectorOpen, setPoiSelectorOpen } = useUiState();
    // 如果没数据，进弹窗重新请求数据
    useEffect(() => {
        setSearchValue('');
        if (poiSelectorOpen && !poiList?.length) {
            pagination.onChange(1, 10);
        }
    }, [poiSelectorOpen]);

    const { send } = useSendMessage();
    const insets = useSafeAreaInsets();
    const { keyboardOffset } = useKeyboard();
    const wrapper = useRef();
    if (!poiSelectorOpen) {
        return null;
    }
    return (
        <TouchableWithoutFeedback
            onPress={(e) => {
                if (e.target === wrapper.current) {
                    setPoiSelectorOpen(false);
                }
            }}
        >
            <View
                ref={wrapper}
                style={[
                    {
                        backgroundColor: 'rgba(34,34,34,0.6)',
                        width: '100%',
                        height: '100%',
                        position: 'absolute',
                        marginBottom: -insets.bottom,
                        paddingBottom: insets.bottom,
                        bottom: 0,
                        left: 0,
                    },
                ]}
            >
                <View
                    style={{
                        maxHeight: Platform.select({
                            ios:
                                Dimensions.get('window').height -
                                keyboardOffset -
                                insets.top,
                            android:
                                Dimensions.get('window').height -
                                keyboardOffset,
                        }),
                        height: 478,
                        position: 'absolute',
                        bottom: Platform.select({
                            ios: keyboardOffset,
                            android: 0,
                        }),
                        left: 0,
                        width: '100%',
                        backgroundColor: '#fff',
                        borderTopLeftRadius: 20,
                        borderTopRightRadius: 20,
                        padding: 10,
                    }}
                >
                    <View
                        style={[
                            TWS.row(),
                            TWS.center(),
                            { height: 50, justifyContent: 'space-between' },
                        ]}
                    >
                        {/* 占位 */}
                        <Text />
                        <Text
                            style={{
                                color: '#222',
                                fontSize: 16,
                                fontWeight: '500',
                            }}
                        >
                            请选择你想查询的商家
                        </Text>
                        <TouchableOpacity
                            onPress={() => {
                                setPoiSelectorOpen(false);
                            }}
                        >
                            <Image
                                source={closeBlackImg}
                                style={[TWS.square(16)]}
                            />
                        </TouchableOpacity>
                    </View>
                    <View
                        style={[
                            TWS.line({
                                width: Dimensions.get('window').width,
                            }),
                            { marginLeft: -10, marginBottom: 12 },
                        ]}
                    />
                    <SearchBar onChange={setSearchValue} />
                    <FlatList
                        refreshControl={
                            <RefreshControl
                                refreshing={false}
                                onRefresh={() => pagination.onChange(1, 10)}
                            />
                        }
                        showsVerticalScrollIndicator={false}
                        style={{ flex: 1 }}
                        keyExtractor={(v) => v.id}
                        onEndReached={() => {
                            if (
                                pagination.current >=
                                Math.ceil(
                                    pagination.total / pagination.pageSize,
                                )
                            ) {
                                return;
                            }
                            pagination.onChange(pagination.current + 1, 10);
                        }}
                        onEndReachedThreshold={0.1}
                        data={poiList}
                        ListEmptyComponent={() => (
                            <View style={[TWS.center(), { marginTop: 100 }]}>
                                <Image
                                    source={noContentImg}
                                    style={[TWS.square(120)]}
                                />
                                <Text
                                    style={{
                                        marginTop: 12,
                                        color: '#999',
                                        fontSize: 14,
                                    }}
                                >
                                    仅支持查询您名下的商家
                                </Text>
                                <Text
                                    style={{
                                        color: '#999',
                                        fontSize: 14,
                                    }}
                                >
                                    请确保输入信息无误
                                </Text>
                            </View>
                        )}
                        renderItem={({ item: { id, name, url }, index }) => {
                            return (
                                <>
                                    <PoiItem
                                        isLast={index === poiList.length - 1}
                                        id={id}
                                        name={name}
                                        url={url}
                                        onPress={() => {
                                            send(
                                                JSON.stringify([
                                                    {
                                                        type: 'config',
                                                        insert: {
                                                            config: {
                                                                style: {
                                                                    backgroundColor:
                                                                        '#fff',
                                                                    width: '100%',
                                                                },
                                                            },
                                                        },
                                                    } as Config,
                                                    {
                                                        type: 'cardWithAvatar',
                                                        insert: {
                                                            cardWithAvatar: {
                                                                type: 'poi',
                                                                avatar: url,
                                                                title: name,
                                                                content: [
                                                                    {
                                                                        label: 'ID',
                                                                        value: String(
                                                                            id,
                                                                        ),
                                                                    },
                                                                ],
                                                            },
                                                        },
                                                    } as CardWithAvatarMessage,
                                                ]),
                                                EntryPointType.POI_SELECTOR,
                                            );
                                            setPoiSelectorOpen(false);
                                        }}
                                    />
                                </>
                            );
                        }}
                    />
                </View>
            </View>
        </TouchableWithoutFeedback>
    );
};
export default PoiSelector;
