// 商家选择器
import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import { apiCaller } from '@mfe/cc-api-caller-bee';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    Dimensions,
    Platform,
    TouchableWithoutFeedback,
    FlatList,
    RefreshControl,
} from '@mrn/react-native';
import { useDebounceEffect, usePagination } from 'ahooks';
import React, { useEffect, useRef, useState } from 'react';

import noContentImg from '../../../assets/images/noContent.png';
import closeBlackImg from '../../../assets/images/poiSelector/closeBlack.png';
import useKeyboard from '../../../hooks/useKeyboard';
import { useSendMessage } from '../../../hooks/useSendMessage';
import { useUiState } from '../../../store/uiState';
import TWS from '../../../TWS';
import { EntryPointType } from '../../../types';
import {
    CardWithAvatarMessage,
    Config,
    SelectorItemMessage,
} from '../../../types/message';
import Condition from '../../Condition/Condition';
import SearchBar from '../../SearchBar';

const getPoiListByPage = async (
    pageNum: number,
    pageSize = 10,
    keyword = '',
) => {
    const res = await apiCaller.get(
        '/bee/v1/bdaiassistant/common/getOwnPoiListByPage',
        { pageSize, pageNum, data: keyword },
    );
    if (res.code === 0) {
        return {
            list: res.data?.poiList || [],
            total: res.data?.total,
        };
    }
    return null;
};
interface PoiItemData {
    id?: string | number;
    name: string;
    url?: string;
    online?: boolean;
    labels?: SelectorItemMessage['insert']['selectorItem']['content'];
    tags?: string[];
}

interface PoiItem extends PoiItemData {
    onPress: () => void;
    isLast?: boolean;
    isMultiSelect?: boolean;
    isSelected?: boolean;
    onToggleSelect?: () => void;
}
export const PoiItem = ({
    onPress,
    isLast,
    id,
    name,
    url,
    online,
    labels,
    tags,
    isMultiSelect,
    isSelected,
    onToggleSelect,
}: PoiItem) => {
    const TheView: any = onPress ? TouchableOpacity : View;
    return (
        <TheView
            onPress={isMultiSelect ? onToggleSelect : onPress}
            style={[
                TWS.row(),
                {
                    borderWidth: 0.5,
                    borderRadius: 10,
                    borderColor: isSelected ? '#4285F4' : '#E7E8E9',
                    backgroundColor: isSelected ? '#F8F9FF' : '#fff',
                    padding: 12,
                    marginBottom: !isLast ? 10 : 0,
                },
            ]}
        >
            {isMultiSelect && (
                <View
                    style={{
                        marginRight: 8,
                        justifyContent: 'center',
                    }}
                >
                    <View
                        style={[
                            TWS.square(20),
                            {
                                borderRadius: 4,
                                borderWidth: 2,
                                borderColor: isSelected ? '#4285F4' : '#E7E8E9',
                                backgroundColor: isSelected
                                    ? '#4285F4'
                                    : '#fff',
                                justifyContent: 'center',
                                alignItems: 'center',
                            },
                        ]}
                    >
                        {isSelected && (
                            <Text
                                style={{
                                    color: '#fff',
                                    fontSize: 12,
                                    fontWeight: 'bold',
                                }}
                            >
                                ✓
                            </Text>
                        )}
                    </View>
                </View>
            )}
            <View
                style={{
                    borderRadius: 8,
                    marginRight: 8,
                    overflow: 'hidden',
                }}
            >
                <Image
                    source={{ uri: url }}
                    style={[TWS.square(36), { borderRadius: 8 }]}
                />
            </View>

            <View style={{ flex: 1 }}>
                <View style={[TWS.row()]}>
                    <Text
                        numberOfLines={1}
                        ellipsizeMode={'middle'}
                        style={{
                            color: '#222',
                            fontSize: 16,
                            flex: 1,
                        }}
                    >
                        {name}
                    </Text>
                    <Condition condition={[online !== undefined]}>
                        <View
                            style={[
                                TWS.row(),
                                TWS.center(),
                                {
                                    borderRadius: 8,
                                    backgroundColor: online
                                        ? '#E6FAF2'
                                        : '#F1F1F1',
                                    paddingHorizontal: 4,
                                    paddingVertical: 2,
                                },
                            ]}
                        >
                            <View
                                style={[
                                    TWS.circle(8),
                                    {
                                        backgroundColor: online
                                            ? '#58C080'
                                            : '#858688',
                                        marginRight: 4,
                                    },
                                ]}
                            />
                            <Text style={{ fontSize: 10 }}>
                                {online ? '在线' : '下线'}
                            </Text>
                        </View>
                    </Condition>
                </View>

                <Condition condition={[Boolean(id)]}>
                    <Text
                        style={{
                            color: '#666',
                            fontSize: 14,
                        }}
                    >
                        ID:{id}
                    </Text>
                </Condition>
                <View
                    style={{
                        flexWrap: 'wrap',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginTop: 2,
                    }}
                >
                    {labels?.map((v) => (
                        <Text style={{ color: '#666' }} key={String(v.value)}>
                            {v.label}:{v.value}
                        </Text>
                    ))}
                    {tags?.map((v) => (
                        <View
                            style={{
                                borderColor: '#666',
                                borderWidth: 0.5,
                                borderRadius: 4,
                                paddingHorizontal: 4,
                                paddingVertical: 2,
                            }}
                            key={v}
                        >
                            <Text style={{ fontSize: 10 }}>{v}</Text>
                        </View>
                    ))}
                </View>
            </View>
        </TheView>
    );
};

// 分隔符组件
const SeparatorItem = ({ title }: { title: string }) => (
    <View style={{ marginVertical: 16 }}>
        <View
            style={[
                TWS.line({ backgroundColor: '#E7E8E9' }),
                { marginBottom: 8 },
            ]}
        />
        <Text style={{ fontSize: 14, color: '#666', textAlign: 'center' }}>
            {title}
        </Text>
        <View
            style={[TWS.line({ backgroundColor: '#E7E8E9' }), { marginTop: 8 }]}
        />
    </View>
);

// 空列表组件
const ListEmptyComponent = () => (
    <View style={[TWS.center(), { marginTop: 100 }]}>
        <Image source={noContentImg} style={[TWS.square(120)]} />
        <Text
            style={{
                marginTop: 12,
                color: '#999',
                fontSize: 14,
            }}
        >
            仅支持查询您名下的商家
        </Text>
        <Text
            style={{
                color: '#999',
                fontSize: 14,
            }}
        >
            请确保输入信息无误
        </Text>
    </View>
);

const PoiSelector = () => {
    const [searchValue, setSearchValue] = useState('');
    const { data, pagination } = usePagination(
        async ({ current, pageSize = 10 }) => {
            const resData = await getPoiListByPage(
                current,
                pageSize,
                searchValue,
            );
            return resData
                ? {
                      list: [
                          ...(current === 1 ? [] : data?.list || []),
                          ...resData.list,
                      ],
                      total: resData.total,
                  }
                : data;
        },
    );
    const { list: poiList } = data || {};
    useDebounceEffect(() => {
        pagination.onChange(1, 10);
    }, [searchValue]);

    const {
        poiSelectorOpen,
        setPoiSelectorOpen,
        poiSelectorMultiSelect = true,
        poiSelectorDefaultList = [
            {
                id: 1767962,
                name: '测试门店主剖熔胯乞荣倡缉沟韧',
                url: 'http://p0.meituan.net/xianfu/d9089ea772c3f4df7e3fd2c8f180270a28672.jpg',
                type: 'poi',
            },
            {
                id: 1803266,
                name: '测试门店廷爱榆瘪钓搀联遣钥训',
                url: 'http://p0.meituan.net/xianfu/d9089ea772c3f4df7e3fd2c8f180270a28672.jpg',
                type: 'poi',
            },
        ],
        poiSelectorSelectedList,
        setPoiSelectorSelectedList,
    } = useUiState();
    // 如果没数据，进弹窗重新请求数据
    useEffect(() => {
        setSearchValue('');
        if (poiSelectorOpen && !poiList?.length) {
            pagination.onChange(1, 10);
        }
    }, [poiSelectorOpen]);

    // 合并默认列表和搜索结果
    const combinedPoiList = React.useMemo(() => {
        const defaultList = searchValue ? [] : poiSelectorDefaultList;
        const searchList = poiList || [];

        // 如果有默认列表且有搜索结果，在中间添加分隔符
        if (defaultList.length > 0 && searchList.length > 0) {
            return [
                ...defaultList,
                { id: '__separator__', isSeparator: true, name: '搜索结果' },
                ...searchList,
            ];
        }

        return [...defaultList, ...searchList];
    }, [poiSelectorDefaultList, poiList, searchValue]);

    // 处理选择逻辑
    const handleToggleSelect = (poi: PoiItemData) => {
        const isSelected = poiSelectorSelectedList.some(
            (item) => item.id === poi.id,
        );
        if (isSelected) {
            setPoiSelectorSelectedList(
                poiSelectorSelectedList.filter((item) => item.id !== poi.id),
            );
        } else {
            setPoiSelectorSelectedList([...poiSelectorSelectedList, poi]);
        }
    };

    // 发送多选结果
    const handleConfirmMultiSelect = () => {
        if (poiSelectorSelectedList.length === 0) {
            return;
        }

        const messages = poiSelectorSelectedList
            .map((poi) => [
                {
                    type: 'config',
                    insert: {
                        config: {
                            style: {
                                backgroundColor: '#fff',
                                width: '100%',
                            },
                        },
                    },
                } as Config,
                {
                    type: 'cardWithAvatar',
                    insert: {
                        cardWithAvatar: {
                            type: 'poi',
                            avatar: poi.url,
                            title: poi.name,
                            content: [
                                {
                                    label: 'ID',
                                    value: String(poi.id),
                                },
                            ],
                        },
                    },
                } as CardWithAvatarMessage,
            ])
            .flat();

        send(JSON.stringify(messages), EntryPointType.POI_SELECTOR);
        setPoiSelectorOpen(false);
    };

    const { send } = useSendMessage();
    const insets = useSafeAreaInsets();
    const { keyboardOffset } = useKeyboard();
    const wrapper = useRef();
    if (!poiSelectorOpen) {
        return null;
    }
    return (
        <TouchableWithoutFeedback
            onPress={(e) => {
                if (e.target === wrapper.current) {
                    setPoiSelectorOpen(false);
                }
            }}
        >
            <View
                ref={wrapper}
                style={[
                    {
                        backgroundColor: 'rgba(34,34,34,0.6)',
                        width: '100%',
                        height: '100%',
                        position: 'absolute',
                        marginBottom: -insets.bottom,
                        paddingBottom: insets.bottom,
                        bottom: 0,
                        left: 0,
                    },
                ]}
            >
                <View
                    style={{
                        maxHeight: Platform.select({
                            ios:
                                Dimensions.get('window').height -
                                keyboardOffset -
                                insets.top,
                            android:
                                Dimensions.get('window').height -
                                keyboardOffset,
                        }),
                        height: 478,
                        position: 'absolute',
                        bottom: Platform.select({
                            ios: keyboardOffset,
                            android: 0,
                        }),
                        left: 0,
                        width: '100%',
                        backgroundColor: '#fff',
                        borderTopLeftRadius: 20,
                        borderTopRightRadius: 20,
                        padding: 10,
                    }}
                >
                    <View
                        style={[
                            TWS.row(),
                            TWS.center(),
                            { height: 50, justifyContent: 'space-between' },
                        ]}
                    >
                        {/* 占位 */}
                        <Text />
                        <Text
                            style={{
                                color: '#222',
                                fontSize: 16,
                                fontWeight: '500',
                            }}
                        >
                            {poiSelectorMultiSelect
                                ? `请选择商家 ${
                                      poiSelectorSelectedList.length > 0
                                          ? `(已选${poiSelectorSelectedList.length}个)`
                                          : ''
                                  }`
                                : '请选择你想查询的商家'}
                        </Text>
                        <TouchableOpacity
                            onPress={() => {
                                setPoiSelectorOpen(false);
                            }}
                        >
                            <Image
                                source={closeBlackImg}
                                style={[TWS.square(16)]}
                            />
                        </TouchableOpacity>
                    </View>
                    <View
                        style={[
                            TWS.line({
                                width: Dimensions.get('window').width,
                            }),
                            { marginLeft: -10, marginBottom: 12 },
                        ]}
                    />
                    <SearchBar onChange={setSearchValue} />
                    {poiSelectorMultiSelect && (
                        <View style={{ marginBottom: 12 }}>
                            <TouchableOpacity
                                onPress={handleConfirmMultiSelect}
                                disabled={poiSelectorSelectedList.length === 0}
                                style={[
                                    {
                                        backgroundColor:
                                            poiSelectorSelectedList.length > 0
                                                ? '#4285F4'
                                                : '#E7E8E9',
                                        borderRadius: 8,
                                        paddingVertical: 12,
                                        alignItems: 'center',
                                    },
                                ]}
                            >
                                <Text
                                    style={{
                                        color:
                                            poiSelectorSelectedList.length > 0
                                                ? '#fff'
                                                : '#999',
                                        fontSize: 16,
                                        fontWeight: '500',
                                    }}
                                >
                                    确定选择{' '}
                                    {poiSelectorSelectedList.length > 0
                                        ? `(${poiSelectorSelectedList.length})`
                                        : ''}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}
                    <FlatList
                        refreshControl={
                            <RefreshControl
                                refreshing={false}
                                onRefresh={() => pagination.onChange(1, 10)}
                            />
                        }
                        showsVerticalScrollIndicator={false}
                        style={{ flex: 1 }}
                        keyExtractor={(v) => v.id}
                        onEndReached={() => {
                            if (
                                pagination.current >=
                                Math.ceil(
                                    pagination.total / pagination.pageSize,
                                )
                            ) {
                                return;
                            }
                            pagination.onChange(pagination.current + 1, 10);
                        }}
                        onEndReachedThreshold={0.1}
                        data={combinedPoiList}
                        ListEmptyComponent={ListEmptyComponent}
                        renderItem={({ item, index }) => {
                            // 处理分隔符
                            if (item.isSeparator) {
                                return <SeparatorItem title={item.name} />;
                            }

                            const { id, name, url, online, labels, tags } =
                                item;
                            const isSelected = poiSelectorSelectedList.some(
                                (selectedItem) => selectedItem.id === id,
                            );

                            return (
                                <>
                                    <PoiItem
                                        isLast={
                                            index === combinedPoiList.length - 1
                                        }
                                        id={id}
                                        name={name}
                                        url={url}
                                        online={online}
                                        labels={labels}
                                        tags={tags}
                                        isMultiSelect={poiSelectorMultiSelect}
                                        isSelected={isSelected}
                                        onToggleSelect={() =>
                                            handleToggleSelect(item)
                                        }
                                        onPress={() => {
                                            if (!poiSelectorMultiSelect) {
                                                send(
                                                    JSON.stringify([
                                                        {
                                                            type: 'config',
                                                            insert: {
                                                                config: {
                                                                    style: {
                                                                        backgroundColor:
                                                                            '#fff',
                                                                        width: '100%',
                                                                    },
                                                                },
                                                            },
                                                        } as Config,
                                                        {
                                                            type: 'cardWithAvatar',
                                                            insert: {
                                                                cardWithAvatar:
                                                                    {
                                                                        type: 'poi',
                                                                        avatar: url,
                                                                        title: name,
                                                                        content:
                                                                            [
                                                                                {
                                                                                    label: 'ID',
                                                                                    value: String(
                                                                                        id,
                                                                                    ),
                                                                                },
                                                                            ],
                                                                    },
                                                            },
                                                        } as CardWithAvatarMessage,
                                                    ]),
                                                    EntryPointType.POI_SELECTOR,
                                                );
                                                setPoiSelectorOpen(false);
                                            }
                                        }}
                                    />
                                </>
                            );
                        }}
                    />
                </View>
            </View>
        </TouchableWithoutFeedback>
    );
};
export default PoiSelector;
