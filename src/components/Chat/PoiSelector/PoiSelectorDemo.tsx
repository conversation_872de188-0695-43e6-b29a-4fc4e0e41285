// PoiSelector 使用示例
import React from 'react';
import { View, TouchableOpacity, Text } from '@mrn/react-native';
import { useUiState } from '../../../store/uiState';

const PoiSelectorDemo = () => {
    const { openPoiSelector } = useUiState();

    // 示例默认POI列表
    const defaultPoiList = [
        {
            id: 'default_1',
            name: '推荐商家A',
            url: 'https://example.com/poi1.jpg',
            online: true,
            labels: [
                { label: '类型', value: '餐饮' },
                { label: '评分', value: '4.8' },
            ],
        },
        {
            id: 'default_2',
            name: '推荐商家B',
            url: 'https://example.com/poi2.jpg',
            online: false,
            labels: [
                { label: '类型', value: '零售' },
                { label: '评分', value: '4.5' },
            ],
        },
    ];

    return (
        <View style={{ padding: 20 }}>
            <Text
                style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}
            >
                PoiSelector 使用示例
            </Text>

            {/* 单选模式 */}
            <TouchableOpacity
                style={{
                    backgroundColor: '#FFD100',
                    padding: 15,
                    borderRadius: 8,
                    marginBottom: 10,
                }}
                onPress={() => {
                    openPoiSelector({
                        multiSelect: false,
                        defaultList: [],
                    });
                }}
            >
                <Text style={{ color: '#333', textAlign: 'center' }}>
                    打开单选模式
                </Text>
            </TouchableOpacity>

            {/* 多选模式 */}
            <TouchableOpacity
                style={{
                    backgroundColor: '#FFD100',
                    padding: 15,
                    borderRadius: 8,
                    marginBottom: 10,
                }}
                onPress={() => {
                    openPoiSelector({
                        multiSelect: true,
                        defaultList: [],
                    });
                }}
            >
                <Text style={{ color: '#333', textAlign: 'center' }}>
                    打开多选模式
                </Text>
            </TouchableOpacity>

            {/* 带默认列表的单选模式 */}
            <TouchableOpacity
                style={{
                    backgroundColor: '#FFD100',
                    padding: 15,
                    borderRadius: 8,
                    marginBottom: 10,
                }}
                onPress={() => {
                    openPoiSelector({
                        multiSelect: false,
                        defaultList: defaultPoiList,
                    });
                }}
            >
                <Text style={{ color: '#333', textAlign: 'center' }}>
                    单选模式 + 默认列表（合并展示）
                </Text>
            </TouchableOpacity>

            {/* 带默认列表的多选模式 */}
            <TouchableOpacity
                style={{
                    backgroundColor: '#FFD100',
                    padding: 15,
                    borderRadius: 8,
                    marginBottom: 10,
                }}
                onPress={() => {
                    openPoiSelector({
                        multiSelect: true,
                        defaultList: defaultPoiList,
                    });
                }}
            >
                <Text style={{ color: '#333', textAlign: 'center' }}>
                    多选模式 + 默认列表（合并展示）
                </Text>
            </TouchableOpacity>
        </View>
    );
};

export default PoiSelectorDemo;
